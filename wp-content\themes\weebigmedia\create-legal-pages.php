<?php
/**
 * <PERSON><PERSON><PERSON> to create legal pages
 * Run this once to create the missing legal pages
 */

// Only run if WordPress is loaded
if (!function_exists('wp_insert_post')) {
    die('WordPress not loaded');
}

// Create Terms page
$terms_page = array(
    'post_title'    => 'Terms',
    'post_name'     => 'terms',
    'post_content'  => 'Terms of Service content will be displayed using the page template.',
    'post_status'   => 'publish',
    'post_type'     => 'page',
    'post_author'   => 1,
);

// Create Cookies page
$cookies_page = array(
    'post_title'    => 'Cookies',
    'post_name'     => 'cookies',
    'post_content'  => 'Cookie Policy content will be displayed using the page template.',
    'post_status'   => 'publish',
    'post_type'     => 'page',
    'post_author'   => 1,
);

// Create Disclaimer page
$disclaimer_page = array(
    'post_title'    => 'Disclaimer',
    'post_name'     => 'disclaimer',
    'post_content'  => 'Disclaimer content will be displayed using the page template.',
    'post_status'   => 'publish',
    'post_type'     => 'page',
    'post_author'   => 1,
);

// Check if pages exist before creating
$existing_terms = get_page_by_path('terms');
$existing_cookies = get_page_by_path('cookies');
$existing_disclaimer = get_page_by_path('disclaimer');

if (!$existing_terms) {
    $terms_id = wp_insert_post($terms_page);
    echo "Created Terms page with ID: " . $terms_id . "\n";
} else {
    echo "Terms page already exists\n";
}

if (!$existing_cookies) {
    $cookies_id = wp_insert_post($cookies_page);
    echo "Created Cookies page with ID: " . $cookies_id . "\n";
} else {
    echo "Cookies page already exists\n";
}

if (!$existing_disclaimer) {
    $disclaimer_id = wp_insert_post($disclaimer_page);
    echo "Created Disclaimer page with ID: " . $disclaimer_id . "\n";
} else {
    echo "Disclaimer page already exists\n";
}

echo "Legal pages creation complete!\n";
?>
