<?php
/**
 * Generic Page Template
 *
 * @package WeeBigMedia
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php while (have_posts()) : the_post(); ?>
        <section class="page-content">
            <div class="container">
                <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                    <header class="page-header">
                        <h1 class="page-title"><?php the_title(); ?></h1>
                    </header>

                    <div class="page-body">
                        <?php
                        the_content();

                        wp_link_pages(array(
                            'before' => '<div class="page-links">' . esc_html__('Pages:', 'weebigmedia'),
                            'after'  => '</div>',
                        ));
                        ?>
                    </div>
                </article>
            </div>
        </section>
    <?php endwhile; ?>
</main>

<style>
.page-content {
    padding: 80px 0;
    background-color: var(--gray-50);
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.page-title {
    color: var(--primary);
    font-size: 2.5rem;
    margin-bottom: 16px;
}

.page-content {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--white);
    padding: 60px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.page-content p {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: 16px;
}

@media (max-width: 768px) {
    .page-content {
        padding: 40px 30px;
    }
    
    .page-title {
        font-size: 2rem;
    }
}
</style>

<?php get_footer(); ?>
