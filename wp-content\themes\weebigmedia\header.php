<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="<?php echo get_template_directory_uri(); ?>/assets/images/favicon.svg">
    <link rel="icon" type="image/png" href="<?php echo get_template_directory_uri(); ?>/assets/images/favicon.png">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<a class="skip-link screen-reader-text" href="#main"><?php esc_html_e('Skip to content', 'weebigmedia'); ?></a>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-branding">
                    <?php
                    if (has_custom_logo()) {
                        the_custom_logo();
                    } else {
                        ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/logo.svg" alt="<?php bloginfo('name'); ?>" class="site-logo">
                        </a>
                        <?php
                    }
                    ?>
                </div>

                <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="<?php esc_attr_e('Primary Menu', 'weebigmedia'); ?>">
                    <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false" style="display: none;">
                        <span class="sr-only"><?php esc_html_e('Menu', 'weebigmedia'); ?></span>
                        <span class="menu-icon">
                            <span></span>
                            <span></span>
                            <span></span>
                        </span>
                    </button>

                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'menu_class'     => 'nav-menu',
                        'container'      => false,
                        'fallback_cb'    => 'weebigmedia_fallback_menu',
                    ));
                    ?>

                    <div class="header-cta">
                        <a href="#contact" class="btn btn-primary btn-sm">Get Free Quote</a>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <div id="content" class="site-content">
        <?php
        // Add page title for non-front pages
        if (!is_front_page() && !is_home()) :
            ?>
            <div class="page-header">
                <div class="container">
                    <?php if (is_archive()) : ?>
                        <h1 class="page-title"><?php the_archive_title(); ?></h1>
                        <?php if (get_the_archive_description()) : ?>
                            <div class="page-description"><?php the_archive_description(); ?></div>
                        <?php endif; ?>
                    <?php elseif (is_search()) : ?>
                        <h1 class="page-title">
                            <?php printf(esc_html__('Search Results for: %s', 'weebigmedia'), '<span>' . get_search_query() . '</span>'); ?>
                        </h1>
                    <?php elseif (is_404()) : ?>
                        <h1 class="page-title"><?php esc_html_e('Page Not Found', 'weebigmedia'); ?></h1>
                    <?php else : ?>
                        <h1 class="page-title"><?php the_title(); ?></h1>
                    <?php endif; ?>
                </div>
            </div>
            <?php
        endif;

        /**
         * Fallback menu function
         */
        if (!function_exists('weebigmedia_fallback_menu')) {
            function weebigmedia_fallback_menu() {
                echo '<ul class="nav-menu">';
                echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
                echo '<li><a href="' . esc_url(home_url('/services')) . '">Services</a></li>';
                echo '<li><a href="' . esc_url(home_url('/portfolio')) . '">Portfolio</a></li>';
                echo '<li><a href="' . esc_url(home_url('/about')) . '">About</a></li>';
                echo '<li><a href="' . esc_url(home_url('/contact')) . '">Contact</a></li>';
                echo '</ul>';
            }
        }
        ?>
        ?>
