<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="<?php echo get_template_directory_uri(); ?>/assets/images/favicon.svg">
    <link rel="icon" type="image/png" href="<?php echo get_template_directory_uri(); ?>/assets/images/favicon.png">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<a class="skip-link screen-reader-text" href="#main"><?php esc_html_e('Skip to content', 'weebigmedia'); ?></a>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-branding">
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home" class="logo">
                        <svg width="240" height="50" viewBox="0 0 240 50" xmlns="http://www.w3.org/2000/svg">
                            <!-- Background circle -->
                            <circle cx="25" cy="25" r="20" fill="#3b4cca" opacity="0.1"/>
                            <!-- Main circle -->
                            <circle cx="25" cy="25" r="15" fill="#3b4cca"/>
                            <!-- Inner design -->
                            <circle cx="25" cy="25" r="8" fill="#ff6b35"/>
                            <circle cx="25" cy="25" r="4" fill="#ffffff"/>
                            <!-- Text with proper spacing -->
                            <text x="60" y="20" font-family="Arial, sans-serif" font-size="18" font-weight="400" fill="#3b4cca">Wee</text>
                            <text x="95" y="20" font-family="Arial, sans-serif" font-size="18" font-weight="700" fill="#3b4cca">Big</text>
                            <text x="135" y="20" font-family="Arial, sans-serif" font-size="18" font-weight="400" fill="#3b4cca">Media</text>
                            <text x="60" y="38" font-family="Arial, sans-serif" font-size="10" font-weight="400" fill="#666">Web Design Agency</text>
                        </svg>
                    </a>
                </div>

                <nav id="site-navigation" class="main-nav" role="navigation">
                    <ul>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#portfolio">Portfolio</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <div id="content" class="site-content">
        <?php
        // Add page title for non-front pages
        if (!is_front_page() && !is_home()) :
            ?>
            <div class="page-header">
                <div class="container">
                    <?php if (is_archive()) : ?>
                        <h1 class="page-title"><?php the_archive_title(); ?></h1>
                        <?php if (get_the_archive_description()) : ?>
                            <div class="page-description"><?php the_archive_description(); ?></div>
                        <?php endif; ?>
                    <?php elseif (is_search()) : ?>
                        <h1 class="page-title">
                            <?php printf(esc_html__('Search Results for: %s', 'weebigmedia'), '<span>' . get_search_query() . '</span>'); ?>
                        </h1>
                    <?php elseif (is_404()) : ?>
                        <h1 class="page-title"><?php esc_html_e('Page Not Found', 'weebigmedia'); ?></h1>
                    <?php else : ?>
                        <h1 class="page-title"><?php the_title(); ?></h1>
                    <?php endif; ?>
                </div>
            </div>
            <?php
        endif;

        ?>
