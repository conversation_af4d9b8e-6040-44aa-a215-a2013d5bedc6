/*
Theme Name: WeeBigMedia
Description: Professional web design agency theme for WeeBigMedia - clean, modern, and conversion-focused
Author: WeeBigMedia
Version: 2.0.0
License: GPL v2 or later
Text Domain: weebigmedia
*/

/* CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Clean Professional Colors */
    --primary: #3b4cca;
    --primary-hover: #2d3ba3;
    --secondary: #ff6b35;
    --secondary-hover: #e55a2f;

    /* Clean Neutrals */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-primary: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;

    /* Layout */
    --container-max: 1200px;
    --section-padding: 80px 0;
    --border-radius: 8px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    /* Transitions */
    --transition: all 0.3s ease;
}

/* Base Styles */
body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: none;
}

.container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 16px;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 14px 28px;
    background-color: var(--secondary);
    color: var(--white);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn:hover {
    background-color: var(--secondary-hover);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

/* Header */
.site-header {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
}

.logo {
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--primary);
    text-decoration: none;
}

.logo-text {
    color: var(--primary);
}

.logo-text strong {
    font-weight: 700;
    color: var(--primary);
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 32px;
}

.main-nav a {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.main-nav a:hover {
    color: var(--primary);
}

/* Hero Section */
.hero {
    padding: var(--section-padding);
    background-color: var(--gray-50);
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 24px;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Services Section */
.services {
    padding: var(--section-padding);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.service-card {
    text-align: center;
    padding: 40px 20px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.service-icon {
    color: var(--primary);
    margin-bottom: 24px;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 16px;
}

.service-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Process Section */
.process {
    padding: var(--section-padding);
    background-color: var(--gray-50);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.process-step {
    text-align: center;
    padding: 40px 20px;
}

.step-number {
    width: 80px;
    height: 80px;
    background-color: var(--secondary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 auto 24px;
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
}

.process-step h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 16px;
}

.process-step p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Portfolio Section */
.portfolio {
    padding: var(--section-padding);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.portfolio-item {
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.portfolio-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.portfolio-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.portfolio-content {
    padding: 24px;
}

.portfolio-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 12px;
}

.portfolio-content p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Insights Section */
.insights {
    padding: var(--section-padding);
    background-color: var(--gray-50);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.insight-item {
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.insight-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.insight-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.insight-content {
    padding: 24px;
}

.insight-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 16px;
}

.read-more {
    color: var(--secondary);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.read-more:hover {
    color: var(--secondary-hover);
    text-decoration: none;
}

/* Contact Section */
.contact {
    padding: var(--section-padding);
    background-color: var(--primary);
    color: var(--white);
}

.contact .section-header h2 {
    color: var(--white);
}

.contact .section-header p {
    color: rgba(255, 255, 255, 0.9);
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    margin-bottom: 20px;
    transition: var(--transition);
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.15);
}

.contact-form button {
    width: 100%;
    background-color: var(--secondary);
    border: none;
    padding: 16px;
    border-radius: var(--border-radius);
    color: var(--white);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.contact-form button:hover {
    background-color: var(--secondary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .services-grid,
    .process-steps,
    .portfolio-grid,
    .insights-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .main-nav ul {
        flex-direction: column;
        gap: 16px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
    }

    .section-header h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .service-card,
    .process-step {
        padding: 30px 15px;
    }
}

/* Footer */
.site-footer {
    background-color: var(--gray-800);
    color: var(--white);
    padding: 40px 0;
    margin-top: 80px;
}

.footer-bottom {
    text-align: center;
}

.footer-bottom p {
    margin: 0;
    color: var(--gray-300);
    font-size: 0.9rem;
}










