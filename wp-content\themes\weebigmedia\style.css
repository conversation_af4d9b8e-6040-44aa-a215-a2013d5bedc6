/*
Theme Name: WeeBigMedia
Description: Professional web design agency theme for WeeBigMedia - optimized for performance, SEO, and user experience
Author: WeeBigMedia
Version: 1.0.0
License: GPL v2 or later
Text Domain: weebigmedia
*/

/* CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Brand Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #8b5cf6;
    --accent: #06b6d4;
    --accent-light: #67e8f9;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Semantic Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --white: #ffffff;

    /* Typography */
    --font-primary: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Font Sizes - Fluid Typography */
    --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
    --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
    --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
    --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
    --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
    --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
    --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3.5rem);
    --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4.5rem);

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    /* Layout */
    --container-max: 1280px;
    --container-padding: var(--space-6);
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition: 300ms ease;
    --transition-slow: 500ms ease;
}

/* Modern Base Styles */
body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    line-height: 1.7;
    color: var(--gray-700);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Modern Typography Scale */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--space-6);
    color: var(--gray-900);
    letter-spacing: -0.025em;
}

h1 {
    font-size: var(--text-5xl);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: var(--text-4xl);
    font-weight: 700;
}

h3 {
    font-size: var(--text-3xl);
    font-weight: 600;
}

h4 {
    font-size: var(--text-2xl);
    font-weight: 600;
}

h5 {
    font-size: var(--text-xl);
    font-weight: 600;
}

h6 {
    font-size: var(--text-lg);
    font-weight: 600;
}

p {
    margin-bottom: var(--space-6);
    color: var(--gray-600);
    font-size: var(--text-base);
    line-height: 1.8;
}

.lead {
    font-size: var(--text-xl);
    color: var(--gray-700);
    font-weight: 400;
    line-height: 1.7;
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
    text-decoration-color: var(--primary);
    text-underline-offset: 4px;
}

/* Modern Layout Components */
.container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.section {
    padding: var(--space-24) 0;
    position: relative;
}

.section-alt {
    background: var(--white);
    position: relative;
}

.section-alt::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--gray-200) 50%, transparent 100%);
}

/* Modern Grid System */
.grid {
    display: grid;
    gap: var(--space-8);
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Responsive Grid Adjustments */
@media (min-width: 768px) {
    .grid-2 { grid-template-columns: repeat(2, 1fr); }
    .grid-3 { grid-template-columns: repeat(3, 1fr); }
    .grid-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Modern Header */
.site-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

.site-header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) 0;
    min-height: 80px;
}

.site-branding {
    display: flex;
    align-items: center;
}

.site-logo {
    height: 60px;
    width: auto;
    transition: var(--transition);
}

.site-logo:hover {
    transform: scale(1.05);
}

/* Modern Navigation */
.main-navigation {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-8);
    align-items: center;
}

.nav-menu a {
    font-weight: 500;
    color: var(--gray-700);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--border-radius);
    position: relative;
    transition: var(--transition);
    font-size: var(--text-sm);
    letter-spacing: 0.025em;
}

.nav-menu a:hover {
    color: var(--primary);
    background: var(--gray-50);
    text-decoration: none;
    transform: translateY(-1px);
}

.nav-menu a.current {
    color: var(--primary);
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    font-weight: 600;
}

.header-cta {
    margin-left: var(--space-4);
}

.header-cta .btn {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: 600;
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: var(--text-base);
    text-align: center;
    cursor: pointer;
    border: none;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--white);
    text-decoration: none;
}

.btn-secondary {
    background: var(--white);
    color: var(--primary);
    border: 2px solid var(--primary);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
}

.btn-outline {
    background: transparent;
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
}

.btn-outline:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-800);
    text-decoration: none;
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--space-6) var(--space-12);
    font-size: var(--text-lg);
    border-radius: var(--border-radius-xl);
}

/* Stunning Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg,
        var(--gray-900) 0%,
        var(--gray-800) 25%,
        var(--primary-dark) 75%,
        var(--primary) 100%);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, var(--primary) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, var(--secondary) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, var(--accent) 0%, transparent 50%);
    opacity: 0.1;
    animation: heroFloat 20s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: heroGrid 30s linear infinite;
}

@keyframes heroFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

@keyframes heroGrid {
    0% { transform: translateX(0); }
    100% { transform: translateX(-100px); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    padding: var(--space-20) 0;
}

.hero h1 {
    color: var(--white);
    margin-bottom: var(--space-8);
    font-size: clamp(2.5rem, 8vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    letter-spacing: -0.05em;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, var(--white) 0%, var(--accent-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero .lead {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--text-xl);
    margin-bottom: var(--space-12);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-buttons {
    display: flex;
    gap: var(--space-6);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--space-12);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-12);
    margin-top: var(--space-16);
    flex-wrap: wrap;
}

.hero-stat {
    text-align: center;
    color: var(--white);
}

.hero-stat-number {
    font-size: var(--text-3xl);
    font-weight: 800;
    display: block;
    color: var(--accent-light);
}

.hero-stat-label {
    font-size: var(--text-sm);
    opacity: 0.8;
    margin-top: var(--space-2);
}

/* Modern Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
    transform: scaleX(0);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gray-300);
}

.card:hover::before {
    transform: scaleX(1);
}

/* Card Variants */
.card-feature {
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
    border: 2px solid var(--primary);
    position: relative;
}

.card-feature::after {
    content: '✨';
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    font-size: var(--text-lg);
}

.card-testimonial {
    background: var(--gray-50);
    border-left: 4px solid var(--primary);
}

.card-service {
    text-align: center;
    padding: var(--space-12) var(--space-8);
}

.card-portfolio {
    padding: 0;
    overflow: hidden;
}

.card-portfolio img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.card-portfolio:hover img {
    transform: scale(1.05);
}

.card-content {
    padding: var(--space-6);
}

/* Service Icons */
.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--text-2xl);
    box-shadow: var(--shadow-lg);
}

.service-icon svg {
    width: 40px;
    height: 40px;
}

/* Service Features */
.service-features {
    list-style: none;
    padding: 0;
    margin: var(--space-6) 0;
}

.service-features li {
    padding: var(--space-2) 0;
    color: var(--gray-600);
    position: relative;
    padding-left: var(--space-6);
    font-size: var(--text-sm);
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary);
    font-weight: bold;
    font-size: var(--text-base);
}

.service-result {
    margin-top: var(--space-6);
    padding: var(--space-4);
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    border-radius: var(--border-radius-lg);
    text-align: center;
    font-size: var(--text-sm);
}

/* Section Headers */
.section-header {
    margin-bottom: var(--space-16);
}

.section-header.text-center {
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: var(--space-16);
}

.section-header h2 {
    margin-bottom: var(--space-4);
}

.section-header .lead {
    color: var(--gray-600);
    margin-bottom: 0;
}

/* Feature Items */
.feature-item {
    margin-bottom: var(--space-12);
    padding: var(--space-6);
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--primary);
    transition: var(--transition);
}

.feature-item:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-4);
    display: inline-block;
}

.feature-item h3 {
    margin-bottom: var(--space-3);
    color: var(--gray-900);
    font-size: var(--text-xl);
}

.feature-item p {
    margin-bottom: var(--space-4);
    color: var(--gray-600);
}

.feature-stat {
    font-size: var(--text-sm);
    color: var(--primary);
    font-weight: 600;
    background: var(--gray-50);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--border-radius);
    display: inline-block;
}

/* Stats Showcase */
.stats-showcase {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
    margin-bottom: var(--space-12);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: var(--text-4xl);
    font-weight: 800;
    display: block;
    line-height: 1;
    margin-bottom: var(--space-2);
}

.stat-label {
    font-size: var(--text-sm);
    opacity: 0.9;
    font-weight: 500;
}

/* Testimonial Highlight */
.testimonial-highlight {
    background: var(--gray-50);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    border-left: 4px solid var(--accent);
}

.testimonial-highlight blockquote {
    font-size: var(--text-lg);
    font-style: italic;
    color: var(--gray-700);
    margin-bottom: var(--space-4);
    line-height: 1.6;
}

.testimonial-highlight cite {
    font-size: var(--text-sm);
    color: var(--gray-500);
    font-weight: 600;
}

/* Contact Section */
.contact-highlight {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    margin-bottom: var(--space-8);
    text-align: center;
}

.contact-highlight h3 {
    color: var(--white);
    margin-bottom: var(--space-4);
    font-size: var(--text-2xl);
}

.contact-methods {
    margin: var(--space-8) 0;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
}

.contact-icon {
    font-size: var(--text-2xl);
    width: 50px;
    text-align: center;
}

.contact-method strong {
    display: block;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.contact-method a {
    color: var(--primary);
    font-weight: 500;
}

.contact-guarantee {
    background: var(--gray-50);
    padding: var(--space-6);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--accent);
}

.contact-guarantee h4 {
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    font-size: var(--text-lg);
}

.contact-guarantee ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-guarantee li {
    padding: var(--space-2) 0;
    color: var(--gray-600);
    position: relative;
    padding-left: var(--space-6);
}

.contact-guarantee li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Contact Form */
.contact-form-container {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.form-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.form-header h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-3);
    font-size: var(--text-2xl);
}

.form-header p {
    color: var(--gray-600);
    margin-bottom: 0;
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    color: var(--gray-700);
    font-weight: 600;
    font-size: var(--text-sm);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    font-size: var(--text-base);
    transition: var(--transition);
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-400);
}

.form-note {
    text-align: center;
    font-size: var(--text-xs);
    color: var(--gray-500);
    margin-top: var(--space-4);
    margin-bottom: 0;
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* Footer */
.site-footer {
    background-color: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.footer-section p,
.footer-section a {
    color: var(--light-gray);
}

.footer-section a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--medium-gray);
    padding-top: var(--spacing-sm);
    text-align: center;
    color: var(--light-gray);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-4 { margin-bottom: var(--space-4); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-12 { margin-bottom: var(--space-12); }

.mt-0 { margin-top: 0; }
.mt-4 { margin-top: var(--space-4); }
.mt-8 { margin-top: var(--space-8); }
.mt-12 { margin-top: var(--space-12); }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.grid { display: grid; }

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--space-4);
    }

    .hero-content {
        padding: var(--space-16) 0;
    }

    .stats-showcase {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
        min-height: auto;
        padding: var(--space-3) 0;
    }

    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--space-4);
    }

    .hero {
        min-height: 80vh;
        text-align: center;
    }

    .hero-content {
        padding: var(--space-12) 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }

    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }

    .feature-item {
        margin-bottom: var(--space-8);
    }

    .section {
        padding: var(--space-16) 0;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        grid-template-columns: 1fr;
    }

    .btn-lg {
        width: 100%;
        max-width: 300px;
    }

    .stat-card {
        padding: var(--space-6);
    }

    .container {
        padding: 0 var(--space-3);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    left: -9999px;
    z-index: 999999;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--primary-blue);
    color: var(--white);
    text-decoration: none;
}

.skip-link:focus {
    left: 6px;
    top: 7px;
}
