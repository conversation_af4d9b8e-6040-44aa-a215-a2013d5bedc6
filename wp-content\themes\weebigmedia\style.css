/*
Theme Name: WeeBigMedia
Description: Professional web design agency theme for WeeBigMedia - optimized for performance, SEO, and user experience
Author: WeeBigMedia
Version: 1.0.0
License: GPL v2 or later
Text Domain: weebigmedia
*/

/* CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Brand Colors */
    --primary-blue: #1e40af;
    --secondary-blue: #3b82f6;
    --accent-blue: #60a5fa;
    --dark-gray: #1f2937;
    --medium-gray: #64748b;
    --light-gray: #94a3b8;
    --background: #f8fafc;
    --white: #ffffff;
    --success-green: #10b981;
    --warning-orange: #f59e0b;
    --error-red: #ef4444;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-h1: 3rem;
    --font-size-h2: 2.25rem;
    --font-size-h3: 1.75rem;
    --font-size-h4: 1.5rem;
    --font-size-h5: 1.25rem;
    --font-size-h6: 1.125rem;
    --font-size-large: 1.125rem;
    --font-size-regular: 1rem;
    --font-size-small: 0.875rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Base Typography */
body {
    font-family: var(--font-primary);
    font-size: var(--font-size-regular);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--background);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
    color: var(--dark-gray);
}

h1 { font-size: var(--font-size-h1); font-weight: 700; }
h2 { font-size: var(--font-size-h2); }
h3 { font-size: var(--font-size-h3); }
h4 { font-size: var(--font-size-h4); }
h5 { font-size: var(--font-size-h5); }
h6 { font-size: var(--font-size-h6); }

p {
    margin-bottom: var(--spacing-sm);
    color: var(--medium-gray);
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-blue);
}

/* Layout Components */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

.section {
    padding: var(--spacing-2xl) 0;
}

.section-alt {
    background-color: var(--white);
}

/* Header Styles */
.site-header {
    background-color: var(--white);
    box-shadow: var(--box-shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.site-logo {
    height: 50px;
    width: auto;
}

/* Navigation */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin-left: var(--spacing-lg);
}

.nav-menu a {
    font-weight: 500;
    color: var(--dark-gray);
    padding: var(--spacing-xs) 0;
    position: relative;
}

.nav-menu a:hover,
.nav-menu a.current {
    color: var(--primary-blue);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-blue);
    transition: var(--transition);
}

.nav-menu a:hover::after,
.nav-menu a.current::after {
    width: 100%;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    border: none;
    transition: var(--transition);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--secondary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background-color: var(--primary-blue);
    color: var(--white);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: calc(var(--spacing-2xl) + 60px) 0 var(--spacing-2xl);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: clamp(2rem, 5vw, 3.5rem);
}

.hero p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-large);
    margin-bottom: var(--spacing-lg);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Cards */
.card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* Footer */
.site-footer {
    background-color: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.footer-section p,
.footer-section a {
    color: var(--light-gray);
}

.footer-section a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--medium-gray);
    padding-top: var(--spacing-sm);
    text-align: center;
    color: var(--light-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --font-size-h1: 2rem;
        --font-size-h2: 1.75rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 3rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-menu li {
        margin: 0 var(--spacing-sm);
    }
    
    .hero {
        padding: calc(var(--spacing-xl) + 100px) 0 var(--spacing-xl);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    left: -9999px;
    z-index: 999999;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--primary-blue);
    color: var(--white);
    text-decoration: none;
}

.skip-link:focus {
    left: 6px;
    top: 7px;
}
