/**
 * WeeBigMedia Theme JavaScript
 * 
 * @package WeeBigMedia
 * @version 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initializeTheme();
    });

    // Window load
    $(window).on('load', function() {
        handlePageLoad();
    });

    // Window scroll
    $(window).on('scroll', function() {
        handleScroll();
    });

    // Window resize
    $(window).on('resize', function() {
        handleResize();
    });

    /**
     * Initialize theme functionality
     */
    function initializeTheme() {
        initMobileMenu();
        initSmoothScrolling();
        initContactForm();
        initBackToTop();
        initAnimations();
        initPortfolioFilter();
        initTestimonialSlider();
    }

    /**
     * Handle page load events
     */
    function handlePageLoad() {
        // Hide loading spinner if exists
        $('.loading-spinner').fadeOut();
        
        // Trigger animations for visible elements
        triggerVisibleAnimations();
    }

    /**
     * Handle scroll events
     */
    function handleScroll() {
        var scrollTop = $(window).scrollTop();
        
        // Header scroll effect
        if (scrollTop > 100) {
            $('.site-header').addClass('scrolled');
        } else {
            $('.site-header').removeClass('scrolled');
        }
        
        // Back to top button
        if (scrollTop > 300) {
            $('#back-to-top').addClass('show');
        } else {
            $('#back-to-top').removeClass('show');
        }
        
        // Trigger animations for elements coming into view
        triggerScrollAnimations();
    }

    /**
     * Handle resize events
     */
    function handleResize() {
        // Close mobile menu on resize
        if ($(window).width() > 768) {
            $('.main-navigation').removeClass('menu-open');
            $('.menu-toggle').removeClass('active');
        }
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        $('.menu-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('.main-navigation').toggleClass('menu-open');
            $('body').toggleClass('menu-open');
        });

        // Close menu when clicking on a link
        $('.nav-menu a').on('click', function() {
            if ($(window).width() <= 768) {
                $('.menu-toggle').removeClass('active');
                $('.main-navigation').removeClass('menu-open');
                $('body').removeClass('menu-open');
            }
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.main-navigation, .menu-toggle').length) {
                $('.menu-toggle').removeClass('active');
                $('.main-navigation').removeClass('menu-open');
                $('body').removeClass('menu-open');
            }
        });
    }

    /**
     * Initialize smooth scrolling
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    var offset = $('.site-header').outerHeight() + 20;
                    $('html, body').animate({
                        scrollTop: target.offset().top - offset
                    }, 800);
                    return false;
                }
            }
        });

        // Back to top button
        $('#back-to-top').on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 800);
        });
    }

    /**
     * Initialize contact form
     */
    function initContactForm() {
        $('#contact-form').on('submit', function(e) {
            e.preventDefault();
            
            var form = $(this);
            var submitBtn = form.find('button[type="submit"]');
            var originalText = submitBtn.text();
            
            // Validate form
            if (!validateContactForm(form)) {
                return false;
            }
            
            // Show loading state
            submitBtn.text('Sending...').prop('disabled', true);
            
            // Prepare form data
            var formData = {
                action: 'contact_form',
                name: form.find('#name').val(),
                email: form.find('#email').val(),
                subject: form.find('#subject').val(),
                message: form.find('#message').val(),
                nonce: weebigmedia_ajax.nonce
            };
            
            // Send AJAX request
            $.ajax({
                url: weebigmedia_ajax.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        showNotification('Message sent successfully!', 'success');
                        form[0].reset();
                    } else {
                        showNotification('Error sending message. Please try again.', 'error');
                    }
                },
                error: function() {
                    showNotification('Error sending message. Please try again.', 'error');
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        });
    }

    /**
     * Validate contact form
     */
    function validateContactForm(form) {
        var isValid = true;
        
        // Remove previous error states
        form.find('.error').removeClass('error');
        
        // Validate required fields
        form.find('[required]').each(function() {
            var field = $(this);
            var value = field.val().trim();
            
            if (!value) {
                field.addClass('error');
                isValid = false;
            }
        });
        
        // Validate email
        var email = form.find('#email').val();
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            form.find('#email').addClass('error');
            isValid = false;
        }
        
        if (!isValid) {
            showNotification('Please fill in all required fields correctly.', 'error');
        }
        
        return isValid;
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        var notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
        
        $('body').append(notification);
        
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 4000);
    }

    /**
     * Initialize back to top button
     */
    function initBackToTop() {
        // Button is handled in scroll function
    }

    /**
     * Initialize animations
     */
    function initAnimations() {
        // Add animation classes to elements
        $('.card, .service-card, .portfolio-card, .testimonial-card').addClass('animate-on-scroll');
        $('.section-header h2, .section-header p').addClass('animate-on-scroll');
    }

    /**
     * Trigger animations for visible elements
     */
    function triggerVisibleAnimations() {
        $('.animate-on-scroll').each(function() {
            if (isElementInViewport(this)) {
                $(this).addClass('animated');
            }
        });
    }

    /**
     * Trigger animations on scroll
     */
    function triggerScrollAnimations() {
        $('.animate-on-scroll:not(.animated)').each(function() {
            if (isElementInViewport(this)) {
                $(this).addClass('animated');
            }
        });
    }

    /**
     * Check if element is in viewport
     */
    function isElementInViewport(element) {
        var rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Initialize portfolio filter
     */
    function initPortfolioFilter() {
        $('.portfolio-filter').on('click', 'button', function() {
            var filterValue = $(this).attr('data-filter');
            
            // Update active button
            $(this).addClass('active').siblings().removeClass('active');
            
            // Filter portfolio items
            if (filterValue === '*') {
                $('.portfolio-item').show();
            } else {
                $('.portfolio-item').hide();
                $('.portfolio-item' + filterValue).show();
            }
        });
    }

    /**
     * Initialize testimonial slider
     */
    function initTestimonialSlider() {
        if ($('.testimonial-slider').length) {
            $('.testimonial-slider').slick({
                dots: true,
                arrows: false,
                infinite: true,
                speed: 500,
                slidesToShow: 3,
                slidesToScroll: 1,
                autoplay: true,
                autoplaySpeed: 5000,
                responsive: [
                    {
                        breakpoint: 768,
                        settings: {
                            slidesToShow: 1
                        }
                    }
                ]
            });
        }
    }

})(jQuery);

// Additional CSS for animations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add CSS for animations
    var style = document.createElement('style');
    style.textContent = `
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification-success {
            background-color: var(--success-green);
        }
        
        .notification-error {
            background-color: var(--error-red);
        }
        
        .form-group input.error,
        .form-group textarea.error {
            border-color: var(--error-red);
            box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
        }
        
        .site-header.scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .menu-toggle {
            display: none;
            flex-direction: column;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        .menu-toggle span {
            width: 25px;
            height: 3px;
            background-color: var(--dark-gray);
            margin: 3px 0;
            transition: 0.3s;
        }
        
        .menu-toggle.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }
        
        .menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }
        
        .menu-toggle.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }
        
        @media (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            
            .main-navigation .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                box-shadow: var(--box-shadow);
                flex-direction: column;
                padding: 1rem;
            }
            
            .main-navigation.menu-open .nav-menu {
                display: flex;
            }
            
            .nav-menu li {
                margin: 0.5rem 0;
            }
        }
    `;
    document.head.appendChild(style);
});
