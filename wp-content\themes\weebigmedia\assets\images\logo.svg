<?xml version="1.0" encoding="UTF-8"?>
<svg width="320" height="80" viewBox="0 0 320 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Bold geometric background -->
  <rect x="0" y="15" width="50" height="50" rx="12" fill="url(#boldGradient)" transform="rotate(45 25 40)"/>

  <!-- Stylized "W" with bold design -->
  <path d="M15 25 L20 50 L25 30 L30 50 L35 25"
        stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" fill="none"/>

  <!-- Company Name with bold typography -->
  <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" font-weight="900" fill="#ffffff">
    WeeBig
  </text>
  <text x="70" y="52" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="700" fill="#00d4ff" letter-spacing="2px">
    MEDIA
  </text>

  <!-- Bold tagline -->
  <text x="70" y="68" font-family="system-ui, -apple-system, sans-serif" font-size="11" fill="#a0a0a0" font-weight="600">
    AI-Powered Web Design for US Businesses
  </text>

  <!-- Dynamic accent elements -->
  <circle cx="280" cy="20" r="3" fill="#00d4ff" opacity="0.8"/>
  <circle cx="290" cy="35" r="2" fill="#ff6b35" opacity="0.6"/>
  <circle cx="300" cy="50" r="2.5" fill="#7c3aed" opacity="0.7"/>

  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="boldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
