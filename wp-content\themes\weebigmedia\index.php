<?php
/**
 * The main template file
 *
 * @package WeeBigMedia
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php if (is_home() || is_front_page()) : ?>
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1><?php echo get_theme_mod('hero_title', 'Professional Web Design for US Businesses'); ?></h1>
                    <p><?php echo get_theme_mod('hero_subtitle', 'We create stunning, high-performance websites that drive results for your business. From custom design to domain management and Google My Business optimization.'); ?></p>
                    <div class="hero-buttons">
                        <a href="#contact" class="btn btn-primary">Get Started Today</a>
                        <a href="#portfolio" class="btn btn-secondary">View Our Work</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Our Services</h2>
                    <p>Comprehensive web solutions tailored for US businesses</p>
                </div>
                <div class="grid grid-3">
                    <div class="card service-card">
                        <div class="service-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                        </div>
                        <h3>Custom Web Design</h3>
                        <p>Stunning, responsive websites that reflect your brand and engage your customers. Built with modern technologies and optimized for performance.</p>
                        <ul>
                            <li>Responsive Design</li>
                            <li>SEO Optimized</li>
                            <li>Fast Loading</li>
                            <li>Mobile-First Approach</li>
                        </ul>
                    </div>
                    
                    <div class="card service-card">
                        <div class="service-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polygon points="10,8 16,12 10,16 10,8"></polygon>
                            </svg>
                        </div>
                        <h3>Domain & Hosting</h3>
                        <p>Complete domain registration and management services with reliable hosting solutions. We handle the technical details so you can focus on your business.</p>
                        <ul>
                            <li>Domain Registration</li>
                            <li>DNS Management</li>
                            <li>SSL Certificates</li>
                            <li>Reliable Hosting</li>
                        </ul>
                    </div>
                    
                    <div class="card service-card">
                        <div class="service-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                                <line x1="12" y1="22.08" x2="12" y2="12"></line>
                            </svg>
                        </div>
                        <h3>Google My Business</h3>
                        <p>Optimize your local presence with professional Google My Business setup and management. Increase visibility and attract more local customers.</p>
                        <ul>
                            <li>GMB Setup & Optimization</li>
                            <li>Local SEO</li>
                            <li>Review Management</li>
                            <li>Local Citations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Why Choose Us Section -->
        <section class="section section-alt">
            <div class="container">
                <div class="section-header">
                    <h2>Why Choose WeeBigMedia?</h2>
                    <p>10+ years of experience delivering exceptional web solutions</p>
                </div>
                <div class="grid grid-2">
                    <div class="feature-content">
                        <div class="feature-item">
                            <h3>🚀 AI-Powered Innovation</h3>
                            <p>We leverage cutting-edge AI tools while maintaining the human touch that makes your brand unique. Stay ahead of the competition with modern, intelligent solutions.</p>
                        </div>
                        <div class="feature-item">
                            <h3>🎯 US Market Expertise</h3>
                            <p>Specialized knowledge of the US market with competitive pricing advantages. We understand American business culture and customer expectations.</p>
                        </div>
                        <div class="feature-item">
                            <h3>⚡ Performance Focused</h3>
                            <p>Every website we build is optimized for speed, SEO, and conversions. Your success is our priority, and we measure it in results.</p>
                        </div>
                        <div class="feature-item">
                            <h3>🤝 Family Business Values</h3>
                            <p>Built on trust, reliability, and long-term relationships. We treat your business like our own and are committed to your growth.</p>
                        </div>
                    </div>
                    <div class="feature-image">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/team-working.jpg" alt="WeeBigMedia Team" loading="lazy">
                    </div>
                </div>
            </div>
        </section>

        <!-- Portfolio Preview -->
        <section id="portfolio" class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Our Recent Work</h2>
                    <p>See how we've helped businesses like yours succeed online</p>
                </div>
                <?php
                $portfolio_query = new WP_Query(array(
                    'post_type' => 'portfolio',
                    'posts_per_page' => 6,
                    'meta_key' => 'featured',
                    'meta_value' => 'yes'
                ));
                
                if ($portfolio_query->have_posts()) : ?>
                    <div class="grid grid-3">
                        <?php while ($portfolio_query->have_posts()) : $portfolio_query->the_post(); ?>
                            <div class="card portfolio-card">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="portfolio-image">
                                        <?php the_post_thumbnail('portfolio-thumb'); ?>
                                        <div class="portfolio-overlay">
                                            <a href="<?php the_permalink(); ?>" class="btn btn-primary">View Project</a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="portfolio-content">
                                    <h3><?php the_title(); ?></h3>
                                    <p><?php the_excerpt(); ?></p>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    <div class="text-center" style="margin-top: 2rem;">
                        <a href="<?php echo get_post_type_archive_link('portfolio'); ?>" class="btn btn-secondary">View All Projects</a>
                    </div>
                <?php endif; wp_reset_postdata(); ?>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="section section-alt">
            <div class="container">
                <div class="section-header">
                    <h2>What Our Clients Say</h2>
                    <p>Don't just take our word for it</p>
                </div>
                <?php
                $testimonials_query = new WP_Query(array(
                    'post_type' => 'testimonials',
                    'posts_per_page' => 3
                ));
                
                if ($testimonials_query->have_posts()) : ?>
                    <div class="grid grid-3">
                        <?php while ($testimonials_query->have_posts()) : $testimonials_query->the_post(); ?>
                            <div class="card testimonial-card">
                                <div class="testimonial-content">
                                    <div class="testimonial-quote">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                                        </svg>
                                    </div>
                                    <p><?php the_content(); ?></p>
                                    <div class="testimonial-author">
                                        <strong><?php the_title(); ?></strong>
                                        <span><?php echo get_post_meta(get_the_ID(), 'company', true); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php endif; wp_reset_postdata(); ?>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Ready to Get Started?</h2>
                    <p>Let's discuss your project and bring your vision to life</p>
                </div>
                <div class="grid grid-2">
                    <div class="contact-info">
                        <h3>Get in Touch</h3>
                        <p>Ready to take your business online? We're here to help you every step of the way. From initial consultation to ongoing support, we're your trusted web design partner.</p>
                        
                        <div class="contact-methods">
                            <div class="contact-method">
                                <strong>Email:</strong>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div class="contact-method">
                                <strong>Phone:</strong>
                                <a href="tel:+1234567890">+1 (234) 567-890</a>
                            </div>
                            <div class="contact-method">
                                <strong>Response Time:</strong>
                                <span>Within 24 hours</span>
                            </div>
                        </div>
                        
                        <div class="social-links">
                            <a href="#" aria-label="Facebook"><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg></a>
                            <a href="#" aria-label="Twitter"><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg></a>
                            <a href="#" aria-label="LinkedIn"><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg></a>
                        </div>
                    </div>
                    
                    <div class="contact-form-container">
                        <form id="contact-form" class="contact-form">
                            <div class="form-group">
                                <label for="name">Name *</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>
                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

    <?php else : ?>
        <!-- Blog/Archive Content -->
        <div class="container">
            <div class="content-area">
                <?php if (have_posts()) : ?>
                    <div class="posts-grid">
                        <?php while (have_posts()) : the_post(); ?>
                            <article class="card post-card">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="post-thumbnail">
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium'); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                <div class="post-content">
                                    <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                                    <div class="post-meta">
                                        <span class="post-date"><?php echo get_the_date(); ?></span>
                                        <span class="post-author">by <?php the_author(); ?></span>
                                    </div>
                                    <p><?php the_excerpt(); ?></p>
                                    <a href="<?php the_permalink(); ?>" class="btn btn-secondary">Read More</a>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>
                    
                    <?php the_posts_pagination(); ?>
                    
                <?php else : ?>
                    <p>No posts found.</p>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</main>

<?php get_footer(); ?>
