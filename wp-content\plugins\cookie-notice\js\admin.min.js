!function(e){e(function(){if(e(".cn_color").wpColorPicker(),e(document).on("click","input.cn-reset-settings",function(){return confirm(cnArgs.resetToDefaults)}),"privacy-consent"===cnArgs.settingsTab){var n={displayedSources:[],sourceContainers:{},tableContainers:{},init:function(n){for(let t in n){let a=e("#cn_privacy_consent_"+t);a.find("#the-list").attr("id","the-list-"+t),a.find("#table-paging").attr("id","table-paging-"+t),"dynamic"===n[t].type&&(this.sourceContainers[t]=a,this.tableContainers[t]=a.find(".cn-privacy-consent-list-table-container"),!0===n[t].status&&!0===n[t].availability&&this.display(t))}},start:function(e){var n=this;this.tableContainers[e].find(".tablenav-pages a, .manage-column.sortable a, .manage-column.sorted a").on("click",function(t){t.preventDefault();var a=this.search.substring(1),s={paged:n.query(a,"paged")||1,order:n.query(a,"order")||"asc",orderby:n.query(a,"orderby")||"title",search:n.query(a,"search")||""};n.update(e,s)})},display:function(n){if(this.displayedSources.includes(n))return;this.displayedSources.push(n);let t=this,s=this.sourceContainers[n].find(".tablenav .spinner");e.ajax({url:ajaxurl,type:"GET",dataType:"json",data:{nonce:e("#cn_privacy_consent_nonce").val(),action:"cn_privacy_consent_display_table",source:n}}).done(function(s){try{if(s.success){t.tableContainers[n].html(s.data),t.tableContainers[n].find("#the-list").attr("id","the-list-"+n),t.tableContainers[n].find("#table-paging").attr("id","table-paging-"+n),t.tableContainers[n].find("input.cn-privacy-consent-form-status").on("change",a);let i=e("#"+n+"-search-input"),o=e("#"+n+"-search-submit");i.on("keydown",function(e){"Enter"===e.key&&(e.preventDefault(),o.click())}),o.on("click",function(e){e.preventDefault();var a=this.search.substring(1),s={paged:t.query(a,"paged")||1,order:t.query(a,"order")||"asc",orderby:t.query(a,"orderby")||"title",search:i.val()||""};t.update(n,s)}),t.start(n)}else console.log('Loading source "'+n+'" failed.')}catch(c){console.log('Loading source "'+n+'" failed.')}}).always(function(){s.removeClass("is-active"),t.tableContainers[n].find("table").removeClass("loading")})},update:function(n,t){let s=this,i=this.sourceContainers[n].find(".tablenav .spinner");i.addClass("is-active"),this.tableContainers[n].find("table").addClass("loading"),e.ajax({url:ajaxurl,type:"GET",data:{nonce:e("#cn_privacy_consent_nonce").val(),action:"cn_privacy_consent_get_forms",source:n,paged:t.paged,order:t.order,orderby:t.orderby,search:t.search}}).done(function(t){try{t.success?(t.data.rows.length&&s.sourceContainers[n].find("tbody").html(t.data.rows),t.data.column_headers.length&&s.sourceContainers[n].find("thead tr, tfoot tr").html(t.data.column_headers),t.data.pagination.length&&(s.sourceContainers[n].find(".tablenav.bottom .tablenav-pages").html(e(t.data.pagination).html()),s.sourceContainers[n].find("#table-paging").attr("id","table-paging-"+n)),s.tableContainers[n].find("input.cn-privacy-consent-form-status").on("change",a),s.start(n)):console.log("FAILED")}catch(i){console.log("FAILED")}}).always(function(){i.removeClass("is-active"),s.tableContainers[n].find("table").removeClass("loading")})},query:function(e,n){for(var t=e.split("&"),a=0;a<t.length;a++){var s=t[a].split("=");if(s[0]===n)return s[1]}return!1}};if(cnArgs.privacyConsentSources){for(let t in n.init(cnArgs.privacyConsentSources),cnArgs.privacyConsentSources)"static"===cnArgs.privacyConsentSources[t].type&&e("#cn_privacy_consent_"+cnArgs.privacyConsentSources[t].id).find("input.cn-privacy-consent-form-status").on("change",a);e("input.cn-privacy-consent-status").on("change",function(){let t=e(this);if(t.is(":checked")){let a=t.data("source");t.closest("fieldset").find(".cn-privacy-consent-options-container").slideDown("fast"),"dynamic"===cnArgs.privacyConsentSources[a].type&&n.display(a)}else t.closest("fieldset").find(".cn-privacy-consent-options-container").slideUp("fast")}),e("input.cn-privacy-consent-active-type").on("change",function(n){let t=e(n.target),a=t.closest("fieldset").find(".cn-privacy-consent-list-table-container"),s=e('[name="'+e(t).attr("name")+'"]:checked').val();a.length>0&&("all"===s?(a.addClass("apply-all"),a.removeClass("apply-selected")):(a.addClass("apply-selected"),a.removeClass("apply-all")))})}function a(){let n=e(this);n.closest("table").addClass("loading"),e.post(ajaxurl,{action:"cn_privacy_consent_form_status",form_id:n.data("form_id"),source:n.data("source"),status:n.is(":checked")?1:0,nonce:cnArgs.noncePrivacyConsent}).done(function(e){}).always(function(){n.closest("table").removeClass("loading")})}}else if("consent-logs"===cnArgs.settingsTab){function s(n,t){let a=n.find(".pagination-links"),s=a.find(".first-page"),i=a.find(".last-page"),o=a.find(".next-page"),c=a.find(".prev-page"),r=a.find(".current-page"),l=parseInt(a.data("total"))||1;var d=n.find("table tbody"),p=n.find("table").find("tbody tr").toArray(),f=!0;n.pagination({dataSource:p,pageSize:t,showNavigator:!1,showPrevious:!1,showNext:!1,showPageNumbers:!1,callback:function(n,t){if(f){f=!1;return}for(let a of(d.find("tr").hide(),n))e(a).show()}}),s.on("click",function(e){e.preventDefault(),s.addClass("disabled"),i.removeClass("disabled"),o.removeClass("disabled"),c.addClass("disabled"),n.pagination("go",1),r.html(1)}),i.on("click",function(e){e.preventDefault(),s.removeClass("disabled"),i.addClass("disabled"),o.addClass("disabled"),c.removeClass("disabled"),n.pagination("go",l),r.html(l)}),o.on("click",function(e){e.preventDefault(),s.removeClass("disabled"),c.removeClass("disabled"),n.pagination("next");let t=n.pagination("getCurrentPageNum");r.html(t),t===l&&(i.addClass("disabled"),o.addClass("disabled"))}),c.on("click",function(e){e.preventDefault(),i.removeClass("disabled"),o.removeClass("disabled"),n.pagination("previous");let t=n.pagination("getCurrentPageNum");r.html(t),1===t&&(s.addClass("disabled"),c.addClass("disabled"))})}if("cookie"===cnArgs.settingsSection)e('.cn-consent-log-item input[type="checkbox"]').on("change",function(){var n=e(this),t=n.closest("tr"),a=t.attr("id")+"_details",i="#"+a,o=t.attr("id")+"_row";if(n.is(":checked")){if(e("#"+o).remove(),e(i).length&&1===e(i).data("status"))e(i).show();else{var c=null;e(i).length?(e(i).show(),(c=e(i+" .cn-consent-logs-data")).addClass("loading"),c.html('<span class="spinner is-active"></span>')):(t.after(cnArgs.consentLogsTemplate),t.next().attr("id",a),c=e(i+" .cn-consent-logs-data")),e.ajax({url:cnArgs.ajaxURL,type:"POST",dataType:"json",data:{action:"cn_get_cookie_consent_logs",nonce:cnArgs.nonceCookieConsentLogs,date:n.closest("tr").data("date")}}).done(function(n){n.success?(e(i).data("status",1),c.find(".spinner").replaceWith(n.data),c.find("#the-list").attr("id","the-list-"+t.data("date")),s(c,10)):(e(i).data("status",0),c.find(".spinner").replaceWith(cnArgs.consentLogsError))}).fail(function(n){e(i).data("status",0),c.find(".spinner").replaceWith(cnArgs.consentLogsError)}).always(function(e){c.removeClass("loading")})}}else e(i).hide(),e(i).after('<tr id="'+o+'" class="cn-consent-logs-row"><td colspan="6"></td></tr>')});else if("privacy"===cnArgs.settingsSection){let i=e(".cn-privacy-consent-logs-data");e.ajax({url:ajaxurl,type:"POST",dataType:"json",data:{nonce:cnArgs.noncePrivacyConsentLogs,action:"cn_get_privacy_consent_logs"}}).done(function(e){e.success?(i.html(e.data),s(i,20)):i.find(".spinner").replaceWith(cnArgs.consentLogsError)}).fail(function(){i.find(".spinner").replaceWith(cnArgs.consentLogsError)}).always(function(){i.find("table").removeClass("loading")})}}else"settings"===cnArgs.settingsTab&&(e("#cn_app_purge_cache a").on("click",function(n){n.preventDefault();var t=this;e(t).parent().addClass("loading").append('<span class="spinner is-active" style="float: none"></span>');var a={action:"cn_purge_cache",nonce:cnArgs.nonce};cnArgs.network&&(a.cn_network=1),e.ajax({url:cnArgs.ajaxURL,type:"POST",dataType:"json",data:a}).always(function(n){e(t).parent().find(".spinner").remove()})}),e('input[name="cookie_notice_options[global_override]"]').on("change",function(){e(".cookie-notice-settings form").toggleClass("cn-options-disabled")}),e("#cn_refuse_opt").on("change",function(){e(this).is(":checked")?e("#cn_refuse_opt_container").slideDown("fast"):e("#cn_refuse_opt_container").slideUp("fast")}),e("#cn_revoke_cookies").on("change",function(){e(this).is(":checked")?e("#cn_revoke_opt_container").slideDown("fast"):e("#cn_revoke_opt_container").slideUp("fast")}),e("#cn_see_more").on("change",function(){e(this).is(":checked")?e("#cn_see_more_opt").slideDown("fast"):e("#cn_see_more_opt").slideUp("fast")}),e("#cn_on_scroll").on("change",function(){e(this).is(":checked")?e("#cn_on_scroll_offset").slideDown("fast"):e("#cn_on_scroll_offset").slideUp("fast")}),e("#cn_conditional_display_opt").on("change",function(){e(this).is(":checked")?e("#cn_conditional_display_opt_container").slideDown("fast"):e("#cn_conditional_display_opt_container").slideUp("fast")}),e("#cn_see_more_link-custom, #cn_see_more_link-page").on("change",function(){"custom"===e("#cn_see_more_link-custom:checked").val()?e("#cn_see_more_opt_page").slideUp("fast",function(){e("#cn_see_more_opt_link").slideDown("fast")}):"page"===e("#cn_see_more_link-page:checked").val()&&e("#cn_see_more_opt_link").slideUp("fast",function(){e("#cn_see_more_opt_page").slideDown("fast")})}),e("#cn_refuse_code_fields").find("a").on("click",function(n){n.preventDefault(),e("#cn_refuse_code_fields").find("a").removeClass("nav-tab-active"),e(".refuse-code-tab").removeClass("active");var t=e(this).attr("id").replace("-tab","");e("#"+t).addClass("active"),e(this).addClass("nav-tab-active")}),e(document).on("click",".add-rule-group",function(n){n.preventDefault();var t=e("#rules-group-template").html(),a=e("#rules-groups"),s=a.find(".rules-group"),i=s.length>0?parseInt(s.last().attr("id").split("-")[2])+1:1;t=(t=t.replace(/__GROUP_ID__/g,i)).replace(/__RULE_ID__/g,1),a.append('<div class="rules-group" id="rules-group-'+i+'">'+t+"</div>"),a.find(".rules-group").last().fadeIn("fast")}),e(document).on("click",".remove-rule",function(n){n.preventDefault(),1===e(this).closest("tbody").find("tr").length?e(this).closest(".rules-group").fadeOut("fast",function(){e(this).remove()}):e(this).closest("tr").fadeOut("fast",function(){e(this).remove()})}),e(document).on("change",".rule-type",function(){var n=e(this),t=n.closest("tr").find("td.value"),a=t.find("select"),s=t.find(".spinner");a.hide(),s.fadeIn("fast").css("visibility","visible"),e.post(ajaxurl,{action:"cn-get-group-rules-values",cn_param:n.val(),cn_nonce:cnArgs.nonceConditional}).done(function(n){s.hide().css("visibility","hidden");try{var t=e.parseJSON(n);a.find("optgroup").remove(),a.fadeIn("fast").find("option").remove().end().append(t.select)}catch(i){}}).fail(function(){})}))})}(jQuery);