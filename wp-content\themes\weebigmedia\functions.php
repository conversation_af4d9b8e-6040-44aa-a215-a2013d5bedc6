<?php
/**
 * WeeBigMedia Theme Functions
 * 
 * @package WeeBigMedia
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function weebigmedia_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    add_theme_support('customize-selective-refresh-widgets');
    add_theme_support('responsive-embeds');
    
    // Add custom logo support
    add_theme_support('custom-logo', array(
        'height'      => 80,
        'width'       => 280,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'weebigmedia'),
        'footer'  => __('Footer Menu', 'weebigmedia'),
    ));
    
    // Add custom image sizes
    add_image_size('hero-image', 1920, 800, true);
    add_image_size('service-card', 400, 300, true);
    add_image_size('portfolio-thumb', 600, 400, true);
    add_image_size('team-member', 300, 300, true);
}
add_action('after_setup_theme', 'weebigmedia_setup');

/**
 * Enqueue Scripts and Styles
 */
function weebigmedia_scripts() {
    // Enqueue styles
    wp_enqueue_style('weebigmedia-style', get_stylesheet_uri(), array(), '1.0.0');
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap', array(), null);
    
    // Enqueue scripts
    wp_enqueue_script('weebigmedia-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('weebigmedia-main', 'weebigmedia_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('weebigmedia_nonce')
    ));
    
    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'weebigmedia_scripts');

/**
 * Register Widget Areas
 */
function weebigmedia_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'weebigmedia'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'weebigmedia'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 1', 'weebigmedia'),
        'id'            => 'footer-1',
        'description'   => __('Footer widget area 1.', 'weebigmedia'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 2', 'weebigmedia'),
        'id'            => 'footer-2',
        'description'   => __('Footer widget area 2.', 'weebigmedia'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 3', 'weebigmedia'),
        'id'            => 'footer-3',
        'description'   => __('Footer widget area 3.', 'weebigmedia'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'weebigmedia_widgets_init');

/**
 * Custom Post Types
 */
function weebigmedia_custom_post_types() {
    // Portfolio Post Type
    register_post_type('portfolio', array(
        'labels' => array(
            'name'               => __('Portfolio', 'weebigmedia'),
            'singular_name'      => __('Portfolio Item', 'weebigmedia'),
            'menu_name'          => __('Portfolio', 'weebigmedia'),
            'add_new'            => __('Add New', 'weebigmedia'),
            'add_new_item'       => __('Add New Portfolio Item', 'weebigmedia'),
            'edit_item'          => __('Edit Portfolio Item', 'weebigmedia'),
            'new_item'           => __('New Portfolio Item', 'weebigmedia'),
            'view_item'          => __('View Portfolio Item', 'weebigmedia'),
            'search_items'       => __('Search Portfolio', 'weebigmedia'),
            'not_found'          => __('No portfolio items found', 'weebigmedia'),
            'not_found_in_trash' => __('No portfolio items found in Trash', 'weebigmedia'),
        ),
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'portfolio'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-portfolio',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest'       => true,
    ));
    
    // Testimonials Post Type
    register_post_type('testimonials', array(
        'labels' => array(
            'name'               => __('Testimonials', 'weebigmedia'),
            'singular_name'      => __('Testimonial', 'weebigmedia'),
            'menu_name'          => __('Testimonials', 'weebigmedia'),
            'add_new'            => __('Add New', 'weebigmedia'),
            'add_new_item'       => __('Add New Testimonial', 'weebigmedia'),
            'edit_item'          => __('Edit Testimonial', 'weebigmedia'),
            'new_item'           => __('New Testimonial', 'weebigmedia'),
            'view_item'          => __('View Testimonial', 'weebigmedia'),
            'search_items'       => __('Search Testimonials', 'weebigmedia'),
            'not_found'          => __('No testimonials found', 'weebigmedia'),
            'not_found_in_trash' => __('No testimonials found in Trash', 'weebigmedia'),
        ),
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'testimonials'),
        'capability_type'    => 'post',
        'has_archive'        => false,
        'hierarchical'       => false,
        'menu_position'      => 6,
        'menu_icon'          => 'dashicons-format-quote',
        'supports'           => array('title', 'editor', 'thumbnail'),
        'show_in_rest'       => true,
    ));
    
    // Team Members Post Type
    register_post_type('team', array(
        'labels' => array(
            'name'               => __('Team Members', 'weebigmedia'),
            'singular_name'      => __('Team Member', 'weebigmedia'),
            'menu_name'          => __('Team', 'weebigmedia'),
            'add_new'            => __('Add New', 'weebigmedia'),
            'add_new_item'       => __('Add New Team Member', 'weebigmedia'),
            'edit_item'          => __('Edit Team Member', 'weebigmedia'),
            'new_item'           => __('New Team Member', 'weebigmedia'),
            'view_item'          => __('View Team Member', 'weebigmedia'),
            'search_items'       => __('Search Team', 'weebigmedia'),
            'not_found'          => __('No team members found', 'weebigmedia'),
            'not_found_in_trash' => __('No team members found in Trash', 'weebigmedia'),
        ),
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'team'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 7,
        'menu_icon'          => 'dashicons-groups',
        'supports'           => array('title', 'editor', 'thumbnail'),
        'show_in_rest'       => true,
    ));
}
add_action('init', 'weebigmedia_custom_post_types');

/**
 * SEO Optimization Functions
 */
function weebigmedia_seo_meta_tags() {
    if (is_home() || is_front_page()) {
        echo '<meta name="description" content="WeeBigMedia - Professional web design agency specializing in custom websites, domain management, and Google My Business optimization for US businesses.">' . "\n";
        echo '<meta name="keywords" content="web design, website development, domain registration, Google My Business, SEO, digital marketing, US business websites">' . "\n";
    }
    
    // Open Graph tags
    echo '<meta property="og:site_name" content="WeeBigMedia">' . "\n";
    echo '<meta property="og:type" content="website">' . "\n";
    echo '<meta property="og:url" content="' . get_permalink() . '">' . "\n";
    
    // Twitter Card tags
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:site" content="@weebigmedia">' . "\n";
}
add_action('wp_head', 'weebigmedia_seo_meta_tags');

/**
 * Schema.org Structured Data
 */
function weebigmedia_schema_markup() {
    if (is_home() || is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'WeeBigMedia',
            'url' => home_url(),
            'logo' => get_template_directory_uri() . '/assets/images/logo.svg',
            'description' => 'Professional web design agency specializing in custom websites, domain management, and Google My Business optimization for US businesses.',
            'address' => array(
                '@type' => 'PostalAddress',
                'addressCountry' => 'US'
            ),
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'availableLanguage' => 'English'
            ),
            'sameAs' => array(
                'https://facebook.com/weebigmedia',
                'https://twitter.com/weebigmedia',
                'https://linkedin.com/company/weebigmedia'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
    }
}
add_action('wp_head', 'weebigmedia_schema_markup');

/**
 * Performance Optimizations
 */
function weebigmedia_performance_optimizations() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emojis
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');
}
add_action('init', 'weebigmedia_performance_optimizations');

/**
 * Custom Excerpt Length
 */
function weebigmedia_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'weebigmedia_excerpt_length');

/**
 * Custom Excerpt More
 */
function weebigmedia_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'weebigmedia_excerpt_more');

/**
 * Contact Form Handler
 */
function weebigmedia_handle_contact_form() {
    if (!wp_verify_nonce($_POST['nonce'], 'weebigmedia_nonce')) {
        wp_die('Security check failed');
    }
    
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Send email
    $to = get_option('admin_email');
    $headers = array('Content-Type: text/html; charset=UTF-8');
    
    $email_subject = 'New Contact Form Submission: ' . $subject;
    $email_message = '<h3>New Contact Form Submission</h3>';
    $email_message .= '<p><strong>Name:</strong> ' . $name . '</p>';
    $email_message .= '<p><strong>Email:</strong> ' . $email . '</p>';
    $email_message .= '<p><strong>Subject:</strong> ' . $subject . '</p>';
    $email_message .= '<p><strong>Message:</strong></p>';
    $email_message .= '<p>' . nl2br($message) . '</p>';
    
    wp_mail($to, $email_subject, $email_message, $headers);
    
    wp_send_json_success('Message sent successfully!');
}
add_action('wp_ajax_contact_form', 'weebigmedia_handle_contact_form');
add_action('wp_ajax_nopriv_contact_form', 'weebigmedia_handle_contact_form');

/**
 * Customizer Settings
 */
function weebigmedia_customize_register($wp_customize) {
    // Hero Section
    $wp_customize->add_section('hero_section', array(
        'title'    => __('Hero Section', 'weebigmedia'),
        'priority' => 30,
    ));
    
    $wp_customize->add_setting('hero_title', array(
        'default'           => 'Professional Web Design for US Businesses',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_title', array(
        'label'   => __('Hero Title', 'weebigmedia'),
        'section' => 'hero_section',
        'type'    => 'text',
    ));
    
    $wp_customize->add_setting('hero_subtitle', array(
        'default'           => 'We create stunning, high-performance websites that drive results for your business.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('hero_subtitle', array(
        'label'   => __('Hero Subtitle', 'weebigmedia'),
        'section' => 'hero_section',
        'type'    => 'textarea',
    ));
}
add_action('customize_register', 'weebigmedia_customize_register');
